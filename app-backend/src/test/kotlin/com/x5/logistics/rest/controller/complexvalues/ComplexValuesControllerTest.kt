package com.x5.logistics.rest.controller.complexvalues

import com.fasterxml.jackson.databind.ObjectMapper
import com.x5.logistics.data.dictionary.AggregationFunction
import com.x5.logistics.data.dictionary.BiComplexFilter
import com.x5.logistics.data.dictionary.BiComplexParameter
import com.x5.logistics.data.dictionary.BiComplexSettings
import com.x5.logistics.data.dictionary.ValueType
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesCreateRequest
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesGetResponse
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesUpdateRequest
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.service.complexvalues.ComplexValuesService
import com.x5.logistics.util.JwtToken
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.eq
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import org.springframework.web.server.ResponseStatusException

@WebMvcTest(ComplexValuesController::class)
class ComplexValuesControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockBean
    private lateinit var complexValuesService: ComplexValuesService

    private val testUser = "testuser"
    private val testToken = JwtToken(email = "$<EMAIL>")

    private val testSettings = BiComplexSettings(
        expression = "A + B",
        valueType = ValueType.value,
        parameters = listOf(
            BiComplexParameter(
                name = "A",
                function = AggregationFunction.sum,
                filters = listOf(
                    BiComplexFilter(
                        name = "filter1",
                        condition = "equals",
                        values = listOf("value1")
                    )
                )
            ),
            BiComplexParameter(
                name = "B",
                function = AggregationFunction.count,
                filters = emptyList()
            )
        )
    )

    private val testResponse = ComplexValuesGetResponse(
        id = 1,
        name = "Test Complex Value",
        settings = testSettings,
        type = "private",
        owner = testUser,
        updatedAt = "2023-01-01T00:00:00Z",
        deleted = false
    )

    @Test
    fun `getComplexValues without id parameter should return all available values`() {
        runBlocking {
            whenever(complexValuesService.getAllAvailableValues(testUser))
                .thenReturn(listOf(testResponse))

            mockMvc.perform(
                get("/api/bi/complex-value")
                    .requestAttr("token", testToken)
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray)
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Test Complex Value"))
                .andExpect(jsonPath("$[0].type").value("private"))
                .andExpect(jsonPath("$[0].owner").value(testUser))
        }
    }

    @Test
    fun `getComplexValues with id parameter should return values by ids`() {
        runBlocking {
            whenever(complexValuesService.getValuesByIds(listOf(1, 2)))
                .thenReturn(listOf(testResponse))

            mockMvc.perform(
                get("/api/bi/complex-value")
                    .param("id", "1", "2")
                    .requestAttr("token", testToken)
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray)
                .andExpect(jsonPath("$[0].id").value(1))
        }
    }

    @Test
    fun `getComplexValues with non-existent ids should return bad request`() {
        runBlocking {
            whenever(complexValuesService.getValuesByIds(listOf(999)))
                .thenReturn(emptyList())

            mockMvc.perform(
                get("/api/bi/complex-value")
                    .param("id", "999")
                    .requestAttr("token", testToken)
            )
                .andExpect(status().isBadRequest)
        }
    }

    @Test
    fun `getComplexValues with empty id list should return all available values`() {
        runBlocking {
            whenever(complexValuesService.getAllAvailableValues(testUser))
                .thenReturn(listOf(testResponse))

            mockMvc.perform(
                get("/api/bi/complex-value")
                    .param("id", "")
                    .requestAttr("token", testToken)
            )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$").isArray)
        }
    }

    @Test
    fun `createComplexValue with valid request should return created value`() {
        val createRequest = ComplexValuesCreateRequest(
            name = "New Complex Value",
            settings = testSettings
        )

        runBlocking {
            whenever(complexValuesService.createComplexValue(any(), eq(testUser)))
                .thenReturn(testResponse.copy(name = "New Complex Value"))

            mockMvc.perform(
                post("/api/bi/complex-value")
                    .requestAttr("token", testToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(createRequest))
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("New Complex Value"))
        }
    }

    @Test
    fun `createComplexValue with invalid request should return bad request`() {
        val invalidRequest = ComplexValuesCreateRequest(
            name = "", // Invalid: empty name
            settings = testSettings
        )

        mockMvc.perform(
            post("/api/bi/complex-value")
                .requestAttr("token", testToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `createComplexValue with duplicate name should return bad request`() {
        val createRequest = ComplexValuesCreateRequest(
            name = "Duplicate Name",
            settings = testSettings
        )

        runBlocking {
            whenever(complexValuesService.createComplexValue(any(), eq(testUser)))
                .thenThrow(WrongRequestDataException("Показатель с таким именем уже существует"))

            mockMvc.perform(
                post("/api/bi/complex-value")
                    .requestAttr("token", testToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(createRequest))
            )
                .andExpect(status().isBadRequest)
        }
    }

    @Test
    fun `updateComplexValue with valid request should return updated value`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = 1,
            name = "Updated Complex Value",
            settings = testSettings
        )

        runBlocking {
            whenever(complexValuesService.updateComplexValue(any(), eq(testUser)))
                .thenReturn(testResponse.copy(name = "Updated Complex Value"))

            mockMvc.perform(
                put("/api/bi/complex-value")
                    .requestAttr("token", testToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(updateRequest))
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.name").value("Updated Complex Value"))
        }
    }

    @Test
    fun `updateComplexValue with invalid id should return bad request`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = -1, // Invalid: negative id
            name = "Updated Name",
            settings = testSettings
        )

        mockMvc.perform(
            put("/api/bi/complex-value")
                .requestAttr("token", testToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `updateComplexValue with non-existent id should return bad request`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = 999,
            name = "Updated Name",
            settings = testSettings
        )

        runBlocking {
            whenever(complexValuesService.updateComplexValue(any(), eq(testUser)))
                .thenThrow(WrongRequestDataException("Показатель не найден или не принадлежит пользователю"))

            mockMvc.perform(
                put("/api/bi/complex-value")
                    .requestAttr("token", testToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(updateRequest))
            )
                .andExpect(status().isBadRequest)
        }
    }

    @Test
    fun `deleteComplexValue with valid id should return ok`() {
        runBlocking {
            whenever(complexValuesService.deleteComplexValue(1, testUser))
                .thenReturn(Unit)

            mockMvc.perform(
                delete("/api/bi/complex-value/1")
                    .requestAttr("token", testToken)
            )
                .andExpect(status().isOk)
        }
    }

    @Test
    fun `deleteComplexValue with non-existent id should return bad request`() {
        runBlocking {
            whenever(complexValuesService.deleteComplexValue(999, testUser))
                .thenThrow(WrongRequestDataException("Показатель не найден или не принадлежит пользователю"))

            mockMvc.perform(
                delete("/api/bi/complex-value/999")
                    .requestAttr("token", testToken)
            )
                .andExpect(status().isBadRequest)
        }
    }

    @Test
    fun `createComplexValue with too long name should return bad request`() {
        val longName = "a".repeat(51) // Exceeds 50 character limit
        val createRequest = ComplexValuesCreateRequest(
            name = longName,
            settings = testSettings
        )

        mockMvc.perform(
            post("/api/bi/complex-value")
                .requestAttr("token", testToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `createComplexValue with invalid settings should return bad request`() {
        val invalidSettings = BiComplexSettings(
            expression = "", // Invalid: empty expression
            valueType = ValueType.value,
            parameters = emptyList() // Invalid: empty parameters list
        )

        val createRequest = ComplexValuesCreateRequest(
            name = "Valid Name",
            settings = invalidSettings
        )

        mockMvc.perform(
            post("/api/bi/complex-value")
                .requestAttr("token", testToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `updateComplexValue with too long name should return bad request`() {
        val longName = "a".repeat(51) // Exceeds 50 character limit
        val updateRequest = ComplexValuesUpdateRequest(
            id = 1,
            name = longName,
            settings = testSettings
        )

        mockMvc.perform(
            put("/api/bi/complex-value")
                .requestAttr("token", testToken)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `updateComplexValue with duplicate name should return bad request`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = 1,
            name = "Duplicate Name",
            settings = testSettings
        )

        runBlocking {
            whenever(complexValuesService.updateComplexValue(any(), eq(testUser)))
                .thenThrow(WrongRequestDataException("Показатель с таким именем уже существует"))

            mockMvc.perform(
                put("/api/bi/complex-value")
                    .requestAttr("token", testToken)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(updateRequest))
            )
                .andExpect(status().isBadRequest)
        }
    }

    @Test
    fun `getComplexValues with mixed existing and non-existing ids should return existing ones`() {
        runBlocking {
            whenever(complexValuesService.getValuesByIds(listOf(1, 999)))
                .thenReturn(listOf(testResponse)) // Only returns existing one

            mockMvc.perform(
                get("/api/bi/complex-value")
                    .param("id", "1", "999")
                    .requestAttr("token", testToken)
            )
                .andExpect(status().isOk)
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray)
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].id").value(1))
        }
    }

    @Test
    fun `createComplexValue without token should handle gracefully`() {
        val createRequest = ComplexValuesCreateRequest(
            name = "Test Name",
            settings = testSettings
        )

        runBlocking {
            whenever(complexValuesService.createComplexValue(any(), eq("unknown")))
                .thenReturn(testResponse)

            mockMvc.perform(
                post("/api/bi/complex-value")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(objectMapper.writeValueAsString(createRequest))
            )
                .andExpect(status().isOk)
        }
    }

    @Test
    fun `deleteComplexValue with invalid id format should return bad request`() {
        mockMvc.perform(
            delete("/api/bi/complex-value/invalid")
                .requestAttr("token", testToken)
        )
            .andExpect(status().isBadRequest)
    }
}
