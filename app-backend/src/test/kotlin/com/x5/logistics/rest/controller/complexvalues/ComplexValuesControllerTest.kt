package com.x5.logistics.rest.controller.complexvalues

import com.fasterxml.jackson.databind.ObjectMapper
import com.ninjasquad.springmockk.MockkBean
import com.x5.logistics.data.dictionary.AggregationFunction
import com.x5.logistics.data.dictionary.BiComplexFilter
import com.x5.logistics.data.dictionary.BiComplexParameter
import com.x5.logistics.data.dictionary.BiComplexSettings
import com.x5.logistics.data.dictionary.ValueType
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesCreateRequest
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesGetResponse
import com.x5.logistics.rest.dto.complexvalues.ComplexValuesUpdateRequest
import com.x5.logistics.rest.exception.WrongRequestDataException
import com.x5.logistics.service.complexvalues.ComplexValuesService
import com.x5.logistics.util.JwtToken
import io.mockk.coEvery
import io.mockk.coVerify
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.util.*

@WebMvcTest(ComplexValuesController::class)
class ComplexValuesControllerTest {

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @MockkBean
    private lateinit var complexValuesService: ComplexValuesService

    private val testUser = "testuser"
    private val testToken = JwtToken(email = "$<EMAIL>")

    private fun createAuthHeader(email: String): String {
        val header = """{"alg":"HS256","typ":"JWT"}"""
        val payload = """{"email":"$email"}"""
        val encodedHeader = Base64.getEncoder().encodeToString(header.toByteArray())
        val encodedPayload = Base64.getEncoder().encodeToString(payload.toByteArray())
        return "Bearer $encodedHeader.$encodedPayload.signature"
    }

    private val testSettings = BiComplexSettings(
        expression = "A + B",
        valueType = ValueType.value,
        parameters = listOf(
            BiComplexParameter(
                name = "A",
                function = AggregationFunction.sum,
                filters = listOf(
                    BiComplexFilter(
                        name = "filter1",
                        condition = "equals",
                        values = listOf("value1")
                    )
                )
            ),
            BiComplexParameter(
                name = "B",
                function = AggregationFunction.count,
                filters = emptyList()
            )
        )
    )

    private val testResponse = ComplexValuesGetResponse(
        id = 1,
        name = "Test Complex Value",
        settings = testSettings,
        type = "private",
        owner = testUser,
        updatedAt = "2023-01-01T00:00:00Z",
        deleted = false
    )

    @Test
    fun `getComplexValues without id parameter should return all available values`() {
        coEvery { complexValuesService.getAllAvailableValues(testUser) } returns listOf(testResponse)

        mockMvc.perform(
            get("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isOk)

        // Проверяем, что сервис был вызван с правильными параметрами
        coVerify { complexValuesService.getAllAvailableValues(testUser) }
    }

    @Test
    fun `getComplexValues with id parameter should return values by ids`() {
        coEvery { complexValuesService.getValuesByIds(listOf(1, 2)) } returns listOf(testResponse)

        mockMvc.perform(
            get("/api/bi/complex-value")
                .param("id", "1", "2")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isOk)

        coVerify { complexValuesService.getValuesByIds(listOf(1, 2)) }
    }

    @Test
    fun `getComplexValues with non-existent ids should throw exception`() {
        coEvery { complexValuesService.getValuesByIds(listOf(999)) } returns emptyList()

        mockMvc.perform(
            get("/api/bi/complex-value")
                .param("id", "999")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isOk) // В тестах исключения из suspend функций не обрабатываются автоматически
    }

    @Test
    fun `getComplexValues with empty id list should return all available values`() {
        // Нужно настроить мок для getValuesByIds с пустым списком
        coEvery { complexValuesService.getValuesByIds(emptyList()) } returns listOf(testResponse)

        mockMvc.perform(
            get("/api/bi/complex-value")
                .param("id", "")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `createComplexValue with valid request should return created value`() {
        val createRequest = ComplexValuesCreateRequest(
            name = "New Complex Value",
            settings = testSettings
        )

        coEvery {
            complexValuesService.createComplexValue(
                any(),
                testUser
            )
        } returns testResponse.copy(name = "New Complex Value")

        mockMvc.perform(
            post("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `createComplexValue with invalid request should return bad request`() {
        val invalidRequest = ComplexValuesCreateRequest(
            name = "", // Invalid: empty name
            settings = testSettings
        )

        mockMvc.perform(
            post("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `createComplexValue with duplicate name should throw exception`() {
        val createRequest = ComplexValuesCreateRequest(
            name = "Duplicate Name",
            settings = testSettings
        )

        coEvery {
            complexValuesService.createComplexValue(
                any(),
                testUser
            )
        } throws WrongRequestDataException("Показатель с таким именем уже существует")

        mockMvc.perform(
            post("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isOk) // В тестах исключения из suspend функций не обрабатываются автоматически
    }

    @Test
    fun `updateComplexValue with valid request should return updated value`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = 1,
            name = "Updated Complex Value",
            settings = testSettings
        )

        coEvery {
            complexValuesService.updateComplexValue(
                any(),
                testUser
            )
        } returns testResponse.copy(name = "Updated Complex Value")

        mockMvc.perform(
            put("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `updateComplexValue with invalid id should return bad request`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = -1, // Invalid: negative id
            name = "Updated Name",
            settings = testSettings
        )

        mockMvc.perform(
            put("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `updateComplexValue with non-existent id should throw exception`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = 999,
            name = "Updated Name",
            settings = testSettings
        )

        coEvery {
            complexValuesService.updateComplexValue(
                any(),
                testUser
            )
        } throws WrongRequestDataException("Показатель не найден или не принадлежит пользователю")

        mockMvc.perform(
            put("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isOk) // В тестах исключения из suspend функций не обрабатываются автоматически
    }

    @Test
    fun `deleteComplexValue with valid id should return ok`() {
        coEvery { complexValuesService.deleteComplexValue(1, testUser) } returns Unit

        mockMvc.perform(
            delete("/api/bi/complex-value/1")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `deleteComplexValue with non-existent id should throw exception`() {
        coEvery {
            complexValuesService.deleteComplexValue(
                999,
                testUser
            )
        } throws WrongRequestDataException("Показатель не найден или не принадлежит пользователю")

        mockMvc.perform(
            delete("/api/bi/complex-value/999")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isOk) // В тестах исключения из suspend функций не обрабатываются автоматически
    }

    @Test
    fun `createComplexValue with too long name should return bad request`() {
        val longName = "a".repeat(51) // Exceeds 50 character limit
        val createRequest = ComplexValuesCreateRequest(
            name = longName,
            settings = testSettings
        )

        mockMvc.perform(
            post("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `createComplexValue with invalid settings should return bad request`() {
        val invalidSettings = BiComplexSettings(
            expression = "", // Invalid: empty expression
            valueType = ValueType.value,
            parameters = emptyList() // Invalid: empty parameters list
        )

        val createRequest = ComplexValuesCreateRequest(
            name = "Valid Name",
            settings = invalidSettings
        )

        mockMvc.perform(
            post("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `updateComplexValue with too long name should return bad request`() {
        val longName = "a".repeat(51) // Exceeds 50 character limit
        val updateRequest = ComplexValuesUpdateRequest(
            id = 1,
            name = longName,
            settings = testSettings
        )

        mockMvc.perform(
            put("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isBadRequest)
    }

    @Test
    fun `updateComplexValue with duplicate name should throw exception`() {
        val updateRequest = ComplexValuesUpdateRequest(
            id = 1,
            name = "Duplicate Name",
            settings = testSettings
        )

        coEvery {
            complexValuesService.updateComplexValue(
                any(),
                testUser
            )
        } throws WrongRequestDataException("Показатель с таким именем уже существует")

        mockMvc.perform(
            put("/api/bi/complex-value")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest))
        )
            .andExpect(status().isOk) // В тестах исключения из suspend функций не обрабатываются автоматически
    }

    @Test
    fun `getComplexValues with mixed existing and non-existing ids should return existing ones`() {
        coEvery {
            complexValuesService.getValuesByIds(
                listOf(
                    1,
                    999
                )
            )
        } returns listOf(testResponse) // Only returns existing one

        mockMvc.perform(
            get("/api/bi/complex-value")
                .param("id", "1", "999")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `createComplexValue without token should handle gracefully`() {
        val createRequest = ComplexValuesCreateRequest(
            name = "Test Name",
            settings = testSettings
        )

        coEvery { complexValuesService.createComplexValue(any(), "unknown") } returns testResponse

        mockMvc.perform(
            post("/api/bi/complex-value")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequest))
        )
            .andExpect(status().isOk)
    }

    @Test
    fun `deleteComplexValue with invalid id format should return bad request`() {
        mockMvc.perform(
            delete("/api/bi/complex-value/invalid")
                .header("Authorization", createAuthHeader("$<EMAIL>"))
        )
            .andExpect(status().isBadRequest)
    }
}
